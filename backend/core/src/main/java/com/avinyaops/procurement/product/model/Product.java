package com.avinyaops.procurement.product.model;

import com.avinyaops.procurement.audit.BaseAuditableEntity;
import com.avinyaops.procurement.idgenerator.AvinyaId;
import com.avinyaops.procurement.product.category.model.ProductCategory;
import com.avinyaops.procurement.product.category.model.ProductSubCategory;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.JoinColumn;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

import org.hibernate.annotations.SQLRestriction;

@Entity
@Table(name = "products")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@SQLRestriction("deleted_date IS NULL")
public class Product extends BaseAuditableEntity {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "id", nullable = false)
    @AvinyaId
    private Long id;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "description", length = 1000, nullable = false)
    private String description;

    @Column(name = "price", nullable = false, precision = 10, scale = 2)
    private BigDecimal price;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "category_id", nullable = false)
    private ProductCategory category;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "sub_category_id", nullable = false)
    private ProductSubCategory subCategory;

    // TODO: find out whether this should be a join
    @Column(name = "organization_id", nullable = false)
    private Long organizationId;

    // @ElementCollection
    // @CollectionTable(name = "product_image_file_ids", joinColumns =
    // @JoinColumn(name = "product_id"))
    // @Column(name = "image_file_id")
    // private List<String> imageFileIds;
    @Column(name = "image_file_id")
    private String imageFileId;
}
