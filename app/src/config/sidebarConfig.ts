import { ROUTES } from '@/constants/routes.constant';
import { MenuItem } from 'primereact/menuitem';

export interface SidebarConfigItem extends MenuItem {
  to?: string;
  items?: SidebarConfigItem[];
}

export const sidebarConfig: SidebarConfigItem[] = [
  {
    label: 'Dashboard',
    icon: 'pi pi-home',
    to: ROUTES.PRIVATE.APP+ROUTES.PRIVATE.DASHBOARD,
  },
  {
    label: 'Purchase Request',
    icon: 'pi pi-cart-plus',
    to: '/users',
  },
  // {
  //   label: 'Vendor RFQ',
  //   icon: 'pi pi-list',
  //   to: '/users',
  // },
  {
    label: 'A<PERSON>ya Items',
    icon: 'pi pi-tags',
    to: '/users',
  },
  {
    label: 'Organization Items',
    icon: 'pi pi-list',
    to: ROUTES.PRIVATE.APP+ROUTES.PRIVATE.ORGANIZATION_ITEM_CATALOG,
  },
  {
    label: 'Organization Settings',
    icon: 'pi pi-cog',
    items: [
      {
        label: 'Overview',
        icon: 'pi pi-image',
        to: ROUTES.PRIVATE.APP+ROUTES.PRIVATE.ORGANIZATION_DETAILS,
      },
      {
        label: 'Employees',
        icon: 'pi pi-users',
        to: ROUTES.PRIVATE.APP+ROUTES.PRIVATE.EMPLOYEE_DETAILS,
      },
      {
        label: 'Designations',
        icon: 'pi pi-id-card',
        to: ROUTES.PRIVATE.APP+ROUTES.PRIVATE.DESIGNATION_DETAILS,
      },
      {
        label: 'Departments',
        icon: 'pi pi-sitemap',
        to: ROUTES.PRIVATE.APP+ROUTES.PRIVATE.DEPARTMENT_DETAILS,
      },
      {
        label: 'Locations',
        icon: 'pi pi-map-marker',
        to: ROUTES.PRIVATE.APP+ROUTES.PRIVATE.LOCATION_DETAILS,
      },
      {
        label: 'Approval Workflows',
        icon: 'pi pi-check-circle',
        to: ROUTES.PRIVATE.APP+ROUTES.PRIVATE.APPROVAL_WORKFLOWS,
      },
    ],
  },
];