import React, { useState, useRef } from 'react';
import { useNavigate } from '@tanstack/react-router';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { InputText } from 'primereact/inputtext';
import Card from '@/components/ui/Card/Card';
import Button from '@/components/ui/Button/Button';
import Modal from '@/components/ui/Modal/Modal';
import Toast, { ToastRef } from '@/components/ui/Toast/Toast';
import { ApprovalWorkflow } from '@/types/approvalWorkflow.types';
import { mockWorkflows } from '@/data/mockApprovalData';
import { addApprovalWorkflowRoute } from '@/routes/private/addApprovalWorkflow.route';
import { editApprovalWorkflowRoute } from '@/routes/private/editApprovalWorkflow.route';
import { ROUTES } from '@/constants/routes.constant';
import './ApprovalWorkflows.css';

const ApprovalWorkflows: React.FC = () => {
  const navigate = useNavigate();
  const toast = useRef<ToastRef>(null);

  const [workflows, setWorkflows] = useState<ApprovalWorkflow[]>(mockWorkflows);
  const [globalFilter, setGlobalFilter] = useState<string>('');
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [workflowToDelete, setWorkflowToDelete] = useState<ApprovalWorkflow | null>(null);
  const [loading, setLoading] = useState(false);

  // Handle creating new workflow
  const handleCreateWorkflow = () => {
    navigate({ to: addApprovalWorkflowRoute.to });
  };

  // Handle editing existing workflow
  const handleEditWorkflow = (workflow: ApprovalWorkflow) => {
    navigate({
      to: `${ROUTES.PRIVATE.APP}${ROUTES.PRIVATE.APPROVAL_WORKFLOWS}${ROUTES.PRIVATE.EDIT_APPROVAL_WORKFLOW}/${workflow.id}`
    });
  };

  // Handle workflow deletion
  const handleDeleteWorkflow = (workflow: ApprovalWorkflow) => {
    setWorkflowToDelete(workflow);
    setIsDeleteModalOpen(true);
  };

  const confirmDelete = async () => {
    if (!workflowToDelete) return;

    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));

      const updatedWorkflows = workflows.filter(w => w.id !== workflowToDelete.id);
      setWorkflows(updatedWorkflows);

      toast.current?.show({
        severity: 'success',
        summary: 'Success',
        detail: 'Workflow deleted successfully',
        life: 3000
      });

      setIsDeleteModalOpen(false);
      setWorkflowToDelete(null);
    } catch (error) {
      toast.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to delete workflow',
        life: 3000
      });
    } finally {
      setLoading(false);
    }
  };

  // Toggle workflow active status
  const toggleWorkflowStatus = async (workflow: ApprovalWorkflow) => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));

      const updatedWorkflows = workflows.map(w =>
        w.id === workflow.id
          ? { ...w, isActive: !w.isActive, lastModifiedDate: new Date().toISOString().split('T')[0] }
          : w
      );
      setWorkflows(updatedWorkflows);

      toast.current?.show({
        severity: 'success',
        summary: 'Success',
        detail: `Workflow ${workflow.isActive ? 'deactivated' : 'activated'} successfully`,
        life: 3000
      });
    } catch (error) {
      toast.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to update workflow status',
        life: 3000
      });
    } finally {
      setLoading(false);
    }
  };

  // Table column renderers
  const nameBodyTemplate = (rowData: ApprovalWorkflow) => {
    return (
      <div>
        <div className="font-medium">{rowData.name}</div>
        <div className="text-sm text-600">{rowData.formType}</div>
      </div>
    );
  };

  const criteriaBodyTemplate = (rowData: ApprovalWorkflow) => {
    const criteriaCount = rowData.criteria.length;
    return (
      <div className="text-sm">
        {criteriaCount} condition{criteriaCount !== 1 ? 's' : ''}
      </div>
    );
  };

  const approvalLevelsBodyTemplate = (rowData: ApprovalWorkflow) => {
    return (
      <div className="text-center">
        <span className="inline-flex align-items-center justify-content-center bg-primary-100 text-primary-800 border-round px-2 py-1 text-sm font-medium">
          Level {rowData.approvalLevels}
        </span>
      </div>
    );
  };

  const statusBodyTemplate = (rowData: ApprovalWorkflow) => {
    return (
      <div className="text-center">
        <span className={`inline-flex align-items-center justify-content-center border-round px-2 py-1 text-sm font-medium ${
          rowData.isActive
            ? 'bg-green-100 text-green-800'
            : 'bg-red-100 text-red-800'
        }`}>
          {rowData.isActive ? 'Active' : 'Inactive'}
        </span>
      </div>
    );
  };

  const actionsBodyTemplate = (rowData: ApprovalWorkflow) => {
    return (
      <div className="flex gap-2">
        <Button
          icon="pi pi-pencil"
          variant="outline"
          size="small"
          onClick={() => handleEditWorkflow(rowData)}
          title="Edit workflow"
        />
        <Button
          icon={rowData.isActive ? "pi pi-pause" : "pi pi-play"}
          variant="outline"
          size="small"
          onClick={() => toggleWorkflowStatus(rowData)}
          title={rowData.isActive ? "Deactivate workflow" : "Activate workflow"}
        />
        <Button
          icon="pi pi-trash"
          variant="danger"
          size="small"
          onClick={() => handleDeleteWorkflow(rowData)}
          title="Delete workflow"
        />
      </div>
    );
  };

  // Table header
  const renderHeader = () => {
    return (
      <div className="flex justify-content-between align-items-center">
        <div className="flex align-items-center gap-2">
          <Button
            icon="pi pi-plus"
            onClick={handleCreateWorkflow}
            className="mr-2"
          >
            New Workflow
          </Button>
        </div>
        <div className="flex align-items-center gap-2">
          <span className="p-input-icon-left">
            <i className="pi pi-search" />
            <InputText
              value={globalFilter}
              onChange={(e) => setGlobalFilter(e.target.value)}
              placeholder="Search workflows..."
              className="w-20rem"
            />
          </span>
        </div>
      </div>
    );
  };

  return (
    <div className="approval-workflows-page">
      <Toast ref={toast} position="top-right" />

      <Card
        title="Approval Workflows"
        subtitle="Configure approval workflows for different form types and criteria"
        variant="elevated"
        padding="large"
      >
        <DataTable
          value={workflows}
          paginator
          rows={10}
          rowsPerPageOptions={[5, 10, 25]}
          globalFilter={globalFilter}
          header={renderHeader()}
          emptyMessage="No workflows found"
          loading={loading}
          className="approval-workflows-table"
        >
          <Column
            field="name"
            header="Workflow Name"
            body={nameBodyTemplate}
            sortable
            style={{ minWidth: '250px' }}
          />
          <Column
            header="Criteria"
            body={criteriaBodyTemplate}
            style={{ minWidth: '120px' }}
          />
          <Column
            header="Approval Levels"
            body={approvalLevelsBodyTemplate}
            sortable
            field="approvalLevels"
            style={{ minWidth: '150px' }}
          />
          <Column
            header="Status"
            body={statusBodyTemplate}
            sortable
            field="isActive"
            style={{ minWidth: '100px' }}
          />
          <Column
            field="lastModifiedDate"
            header="Last Modified"
            sortable
            style={{ minWidth: '150px' }}
          />
          <Column
            header="Actions"
            body={actionsBodyTemplate}
            style={{ minWidth: '150px' }}
          />
        </DataTable>
      </Card>

      {/* Delete Confirmation Modal */}
      <Modal
        visible={isDeleteModalOpen}
        onHide={() => {
          setIsDeleteModalOpen(false);
          setWorkflowToDelete(null);
        }}
        header="Confirm Deletion"
        footerButtons={[
          {
            label: "Cancel",
            onClick: () => {
              setIsDeleteModalOpen(false);
              setWorkflowToDelete(null);
            },
            variant: "outline"
          },
          {
            label: "Delete",
            onClick: confirmDelete,
            variant: "danger",
            isLoading: loading
          }
        ]}
      >
        <div className="confirmation-content">
          <i className="pi pi-exclamation-triangle text-orange-500 text-2xl mr-3"></i>
          <span>
            Are you sure you want to delete the workflow "{workflowToDelete?.name}"?
            This action cannot be undone.
          </span>
        </div>
      </Modal>
    </div>
  );
};

export default ApprovalWorkflows;
