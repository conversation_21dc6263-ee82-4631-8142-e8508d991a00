import React from 'react';
import { RadioButton } from 'primereact/radiobutton';
import { approvalLevelOptions } from '@/data/mockApprovalData';
import './WorkflowLevels.css';

interface WorkflowLevelsProps {
  levels: number;
  onChange: (levels: number) => void;
}

const WorkflowLevels: React.FC<WorkflowLevelsProps> = ({
  levels,
  onChange
}) => {
  // Get description for approval level
  const getLevelDescription = (level: number): string => {
    switch (level) {
      case 1:
        return 'Request goes directly to the employee\'s reporting manager for approval.';
      case 2:
        return 'Request goes to the employee\'s reporting manager, then to their manager\'s reporting manager.';
      case 3:
        return 'Request follows a 3-level hierarchy: employee → direct manager → manager\'s manager → manager\'s manager\'s manager.';
      case 4:
        return 'Request follows a 4-level hierarchy through the complete reporting chain.';
      default:
        return 'Custom approval hierarchy.';
    }
  };

  // Get approval path visualization
  const getApprovalPath = (level: number): string[] => {
    const basePath = ['Employee submits request'];
    
    for (let i = 1; i <= level; i++) {
      if (i === 1) {
        basePath.push('Direct Reporting Manager');
      } else {
        basePath.push(`Level ${i} Manager`);
      }
    }
    
    basePath.push('Request Approved/Rejected');
    return basePath;
  };

  return (
    <div className="workflow-levels">
      {/* Level Selection */}
      <div className="level-selection">
        <h4 className="selection-title">Select Approval Levels:</h4>
        
        <div className="level-options">
          {approvalLevelOptions.map((option) => (
            <div key={option.value} className="level-option">
              <div className="level-radio">
                <RadioButton
                  inputId={`level-${option.value}`}
                  value={option.value}
                  checked={levels === option.value}
                  onChange={(e) => onChange(e.value)}
                />
                <label htmlFor={`level-${option.value}`} className="level-label">
                  {option.label}
                </label>
              </div>
              <div className="level-description">
                {getLevelDescription(option.value)}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Approval Path Visualization */}
      <div className="approval-path">
        <h4 className="path-title">Approval Path Preview:</h4>
        
        <div className="path-visualization">
          {getApprovalPath(levels).map((step, index) => (
            <div key={index} className="path-step">
              <div className="step-number">
                {index + 1}
              </div>
              <div className="step-content">
                <div className="step-title">{step}</div>
                {index === 0 && (
                  <div className="step-description">
                    Employee initiates the request through the system
                  </div>
                )}
                {index > 0 && index < getApprovalPath(levels).length - 1 && (
                  <div className="step-description">
                    {index === 1 
                      ? 'Reviews and approves/rejects the request'
                      : `Level ${index} approval in the hierarchy`
                    }
                  </div>
                )}
                {index === getApprovalPath(levels).length - 1 && (
                  <div className="step-description">
                    Final decision is communicated to the employee
                  </div>
                )}
              </div>
              {index < getApprovalPath(levels).length - 1 && (
                <div className="step-connector">
                  <i className="pi pi-arrow-down"></i>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Additional Information */}
      <div className="level-info">
        <div className="info-card">
          <div className="info-header">
            <i className="pi pi-info-circle text-blue-500"></i>
            <span className="info-title">How Approval Levels Work</span>
          </div>
          <div className="info-content">
            <ul>
              <li>
                <strong>Level 1:</strong> Only the direct reporting manager needs to approve
              </li>
              <li>
                <strong>Level 2+:</strong> Follows the organizational hierarchy upward
              </li>
              <li>
                <strong>Sequential Approval:</strong> Each level must approve before moving to the next
              </li>
              <li>
                <strong>Rejection:</strong> Any level can reject, stopping the workflow
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WorkflowLevels;
