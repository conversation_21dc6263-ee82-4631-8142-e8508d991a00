.workflow-levels {
  width: 100%;
}

.level-selection {
  margin-bottom: 2rem;
}

.selection-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 1rem 0;
}

.level-options {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.level-option {
  padding: 1rem;
  border: 1px solid var(--surface-border);
  border-radius: 6px;
  background: var(--surface-section);
  transition: all 0.2s ease;
}

.level-option:hover {
  border-color: var(--primary-color);
  background: var(--surface-hover);
}

.level-radio {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.level-label {
  font-weight: 500;
  color: var(--text-color);
  cursor: pointer;
  font-size: 0.875rem;
}

.level-description {
  color: var(--text-color-secondary);
  font-size: 0.8125rem;
  line-height: 1.4;
  margin-left: 1.75rem;
}

.approval-path {
  margin-bottom: 2rem;
}

.path-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 1.5rem 0;
}

.path-visualization {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.path-step {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  position: relative;
}

.step-number {
  flex-shrink: 0;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background: var(--primary-color);
  color: var(--primary-color-text);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.step-content {
  flex: 1;
  padding-bottom: 1.5rem;
}

.step-title {
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 0.25rem;
  font-size: 0.875rem;
}

.step-description {
  color: var(--text-color-secondary);
  font-size: 0.8125rem;
  line-height: 1.4;
}

.step-connector {
  position: absolute;
  left: 0.9375rem;
  top: 2.5rem;
  bottom: -0.5rem;
  width: 0.125rem;
  background: var(--surface-border);
  display: flex;
  align-items: center;
  justify-content: center;
}

.step-connector i {
  background: var(--surface-section);
  color: var(--text-color-secondary);
  padding: 0.25rem;
  font-size: 0.75rem;
}

.level-info {
  margin-top: 2rem;
}

.info-card {
  padding: 1.5rem;
  background: var(--blue-50);
  border: 1px solid var(--blue-200);
  border-radius: 6px;
}

.info-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.info-title {
  font-weight: 600;
  color: var(--blue-800);
  font-size: 0.875rem;
}

.info-content ul {
  margin: 0;
  padding-left: 1.25rem;
  color: var(--blue-700);
}

.info-content li {
  margin-bottom: 0.5rem;
  font-size: 0.8125rem;
  line-height: 1.4;
}

.info-content li:last-child {
  margin-bottom: 0;
}

.info-content strong {
  color: var(--blue-800);
}

/* Responsive design */
@media (max-width: 768px) {
  .level-option {
    padding: 0.75rem;
  }
  
  .level-description {
    margin-left: 1.5rem;
  }
  
  .path-step {
    gap: 0.75rem;
  }
  
  .step-number {
    width: 1.75rem;
    height: 1.75rem;
    font-size: 0.8125rem;
  }
  
  .step-connector {
    left: 0.8125rem;
  }
  
  .info-card {
    padding: 1rem;
  }
}

/* Radio button styling */
.workflow-levels .p-radiobutton .p-radiobutton-box {
  border-color: var(--surface-border);
}

.workflow-levels .p-radiobutton .p-radiobutton-box.p-highlight {
  border-color: var(--primary-color);
  background: var(--primary-color);
}

.workflow-levels .p-radiobutton:not(.p-radiobutton-disabled) .p-radiobutton-box:hover {
  border-color: var(--primary-color);
}
