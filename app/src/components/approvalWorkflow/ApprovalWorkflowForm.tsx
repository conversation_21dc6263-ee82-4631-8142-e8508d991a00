import React, { useState, useEffect } from 'react';
import { InputText } from 'primereact/inputtext';
import { Dropdown } from 'primereact/dropdown';
import { Checkbox } from 'primereact/checkbox';
import { Divider } from 'primereact/divider';
import Button from '@/components/ui/Button/Button';
import CriteriaBuilder from './CriteriaBuilder';
import WorkflowLevels from './WorkflowLevels';
import WorkflowPreview from './WorkflowPreview';
import { ApprovalWorkflow, WorkflowCriteria, CreateApprovalWorkflowRequest } from '@/types/approvalWorkflow.types';
import { mockFormTypes } from '@/data/mockApprovalData';
import './ApprovalWorkflowForm.css';

interface ApprovalWorkflowFormProps {
  workflow?: ApprovalWorkflow | null;
  onSubmit: (data: CreateApprovalWorkflowRequest) => void;
  onCancel: () => void;
  loading?: boolean;
}

const ApprovalWorkflowForm: React.FC<ApprovalWorkflowFormProps> = ({
  workflow,
  onSubmit,
  onCancel,
  loading = false
}) => {
  const [formData, setFormData] = useState({
    name: '',
    formType: '',
    criteria: [] as WorkflowCriteria[],
    approvalLevels: 1,
    autoApprove: false,
    autoReject: false
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // Initialize form data when workflow prop changes
  useEffect(() => {
    if (workflow) {
      setFormData({
        name: workflow.name,
        formType: workflow.formType,
        criteria: workflow.criteria,
        approvalLevels: workflow.approvalLevels,
        autoApprove: workflow.autoApprove,
        autoReject: workflow.autoReject
      });
    } else {
      setFormData({
        name: '',
        formType: '',
        criteria: [],
        approvalLevels: 1,
        autoApprove: false,
        autoReject: false
      });
    }
    setErrors({});
  }, [workflow]);

  // Validation
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Workflow name is required';
    }

    if (!formData.formType) {
      newErrors.formType = 'Form type is required';
    }

    if (formData.criteria.length === 0) {
      newErrors.criteria = 'At least one criteria is required';
    }

    if (formData.autoApprove && formData.autoReject) {
      newErrors.autoActions = 'Cannot enable both auto-approve and auto-reject';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      onSubmit(formData);
    }
  };

  // Handle input changes
  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  // Handle criteria changes
  const handleCriteriaChange = (criteria: WorkflowCriteria[]) => {
    handleInputChange('criteria', criteria);
  };

  // Handle approval levels change
  const handleApprovalLevelsChange = (levels: number) => {
    handleInputChange('approvalLevels', levels);
  };

  return (
    <div className="approval-workflow-form">
      <form onSubmit={handleSubmit}>
        {/* Basic Information Section */}
        <div className="form-section">
          <h3 className="section-title">Basic Information</h3>
          
          <div className="form-grid">
            <div className="form-field">
              <label htmlFor="workflow-name" className="field-label">
                Workflow Name <span className="required">*</span>
              </label>
              <InputText
                id="workflow-name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Enter workflow name"
                className={`w-full ${errors.name ? 'p-invalid' : ''}`}
              />
              {errors.name && <small className="p-error">{errors.name}</small>}
            </div>

            <div className="form-field">
              <label htmlFor="form-type" className="field-label">
                Form Type <span className="required">*</span>
              </label>
              <Dropdown
                id="form-type"
                value={formData.formType}
                options={mockFormTypes}
                onChange={(e) => handleInputChange('formType', e.value)}
                placeholder="Select form type"
                className={`w-full ${errors.formType ? 'p-invalid' : ''}`}
                filter
              />
              {errors.formType && <small className="p-error">{errors.formType}</small>}
            </div>
          </div>
        </div>

        <Divider />

        {/* Criteria Section */}
        <div className="form-section">
          <h3 className="section-title">Criteria Configuration</h3>
          <p className="section-description">
            Define the conditions that will trigger this approval workflow.
          </p>
          
          <CriteriaBuilder
            criteria={formData.criteria}
            onChange={handleCriteriaChange}
            error={errors.criteria}
          />
        </div>

        <Divider />

        {/* Workflow Levels Section */}
        <div className="form-section">
          <h3 className="section-title">Approval Levels</h3>
          <p className="section-description">
            Configure the number of approval levels and hierarchy.
          </p>
          
          <WorkflowLevels
            levels={formData.approvalLevels}
            onChange={handleApprovalLevelsChange}
          />
        </div>

        <Divider />

        {/* Auto Actions Section */}
        <div className="form-section">
          <h3 className="section-title">Automatic Actions</h3>
          <p className="section-description">
            Configure automatic approval or rejection for matching criteria.
          </p>
          
          <div className="auto-actions-grid">
            <div className="field-checkbox">
              <Checkbox
                id="auto-approve"
                checked={formData.autoApprove}
                onChange={(e) => handleInputChange('autoApprove', e.checked)}
                disabled={formData.autoReject}
              />
              <label htmlFor="auto-approve" className="ml-2">
                Auto-approve requests matching criteria
              </label>
            </div>

            <div className="field-checkbox">
              <Checkbox
                id="auto-reject"
                checked={formData.autoReject}
                onChange={(e) => handleInputChange('autoReject', e.checked)}
                disabled={formData.autoApprove}
              />
              <label htmlFor="auto-reject" className="ml-2">
                Auto-reject requests matching criteria
              </label>
            </div>
          </div>

          {errors.autoActions && (
            <small className="p-error">{errors.autoActions}</small>
          )}
        </div>

        <Divider />

        Workflow Preview Section
        <div className="form-section">
          <h3 className="section-title">Workflow Preview</h3>
          <p className="section-description">
            Preview how this workflow will operate.
          </p>
          
          <WorkflowPreview
            criteria={formData.criteria}
            approvalLevels={formData.approvalLevels}
            autoApprove={formData.autoApprove}
            autoReject={formData.autoReject}
          />
        </div>

        {/* Form Actions */}
        <div className="form-actions">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            isLoading={loading}
            disabled={loading}
          >
            {workflow ? 'Update Workflow' : 'Create Workflow'}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default ApprovalWorkflowForm;
