.workflow-preview {
  width: 100%;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 500;
  font-size: 0.875rem;
  margin-bottom: 1.5rem;
}

.status-success {
  background: var(--green-100);
  color: var(--green-800);
  border: 1px solid var(--green-200);
}

.status-danger {
  background: var(--red-100);
  color: var(--red-800);
  border: 1px solid var(--red-200);
}

.status-warning {
  background: var(--orange-100);
  color: var(--orange-800);
  border: 1px solid var(--orange-200);
}

.status-info {
  background: var(--blue-100);
  color: var(--blue-800);
  border: 1px solid var(--blue-200);
}

.preview-section {
  margin-bottom: 2rem;
  border: 1px solid var(--surface-border);
  border-radius: 6px;
  overflow: hidden;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  background: var(--surface-100);
  border-bottom: 1px solid var(--surface-border);
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color);
}

.section-content {
  padding: 1.5rem;
}

.criteria-display {
  background: var(--surface-50);
  padding: 1rem;
  border-radius: 4px;
  border-left: 4px solid var(--primary-color);
}

.criteria-text {
  margin: 0;
  line-height: 1.5;
  color: var(--text-color);
  font-size: 0.875rem;
}

.empty-state {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-color-secondary);
  font-style: italic;
  font-size: 0.875rem;
}

.approval-flow {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.flow-step {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  position: relative;
}

.step-indicator {
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.75rem;
  height: 1.75rem;
  background: var(--primary-color);
  color: var(--primary-color-text);
  border-radius: 50%;
  font-weight: 600;
  font-size: 0.8125rem;
}

.step-content {
  flex: 1;
  padding-bottom: 1.25rem;
}

.step-text {
  color: var(--text-color);
  font-size: 0.875rem;
  line-height: 1.5;
}

.step-arrow {
  position: absolute;
  left: 0.8125rem;
  top: 2rem;
  bottom: -0.25rem;
  width: 0.125rem;
  background: var(--surface-border);
  display: flex;
  align-items: center;
  justify-content: center;
}

.step-arrow i {
  background: var(--surface-section);
  color: var(--text-color-secondary);
  padding: 0.25rem;
  font-size: 0.75rem;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.summary-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 1rem;
  background: var(--surface-50);
  border-radius: 4px;
  border: 1px solid var(--surface-border);
}

.summary-label {
  font-size: 0.8125rem;
  color: var(--text-color-secondary);
  font-weight: 500;
}

.summary-value {
  font-size: 0.875rem;
  color: var(--text-color);
  font-weight: 600;
}

/* Responsive design */
@media (max-width: 768px) {
  .section-title {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
  }
  
  .section-content {
    padding: 1rem;
  }
  
  .criteria-display {
    padding: 0.75rem;
  }
  
  .flow-step {
    gap: 0.75rem;
  }
  
  .step-number {
    width: 1.5rem;
    height: 1.5rem;
    font-size: 0.75rem;
  }
  
  .step-arrow {
    left: 0.6875rem;
  }
  
  .summary-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
  
  .summary-item {
    padding: 0.75rem;
  }
  
  .status-badge {
    padding: 0.375rem 0.75rem;
    font-size: 0.8125rem;
  }
}

/* Animation for status badge */
.status-badge {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
